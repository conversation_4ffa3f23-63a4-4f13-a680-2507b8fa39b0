import { dbManager } from './indexedDB';
import { Order } from '@/types/order';
import { ProductTemplate } from '@/types/product';
import { BusinessInfo } from '@/types/business';
import { User } from '@/types/auth';

// 数据迁移管理器
export class DataMigrationManager {
  private static instance: DataMigrationManager;
  private migrationCompleted = false;

  static getInstance(): DataMigrationManager {
    if (!DataMigrationManager.instance) {
      DataMigrationManager.instance = new DataMigrationManager();
    }
    return DataMigrationManager.instance;
  }

  // 检查是否需要迁移
  private needsMigration(): boolean {
    // 检查 localStorage 中是否有数据
    const hasOrderData = localStorage.getItem('order-store');
    const hasProductData = localStorage.getItem('product-store');
    const hasBusinessData = localStorage.getItem('business-store');
    const hasAuthData = localStorage.getItem('auth-store');
    const hasThemeData = localStorage.getItem('theme-store');

    return !!(hasOrderData || hasProductData || hasBusinessData || hasAuthData || hasThemeData);
  }

  // 执行数据迁移
  async migrate(): Promise<void> {
    if (this.migrationCompleted || !this.needsMigration()) {
      return;
    }

    console.log('开始数据迁移...');

    try {
      // 初始化数据库
      await dbManager.init();

      // 迁移订单数据
      await this.migrateOrders();

      // 迁移商品数据
      await this.migrateProducts();

      // 迁移商业信息
      await this.migrateBusiness();

      // 迁移用户数据
      await this.migrateAuth();

      // 迁移设置数据
      await this.migrateSettings();

      // 标记迁移完成
      await this.markMigrationCompleted();

      // 自动清理localStorage数据
      await this.cleanupLocalStorage();

      console.log('数据迁移完成');
      this.migrationCompleted = true;

    } catch (error) {
      console.error('数据迁移失败:', error);
      throw error;
    }
  }

  // 迁移订单数据
  private async migrateOrders(): Promise<void> {
    const orderStoreData = localStorage.getItem('order-store');
    if (!orderStoreData) return;

    try {
      const parsed = JSON.parse(orderStoreData);
      const orders: Order[] = parsed.state?.orders || [];

      for (const order of orders) {
        await dbManager.put('orders', order);
      }

      console.log(`迁移了 ${orders.length} 个订单`);
    } catch (error) {
      console.error('迁移订单数据失败:', error);
    }
  }

  // 迁移商品数据
  private async migrateProducts(): Promise<void> {
    const productStoreData = localStorage.getItem('product-store');
    if (!productStoreData) return;

    try {
      const parsed = JSON.parse(productStoreData);
      const products: ProductTemplate[] = parsed.state?.templates || [];

      for (const product of products) {
        await dbManager.put('products', product);
      }

      console.log(`迁移了 ${products.length} 个商品模板`);
    } catch (error) {
      console.error('迁移商品数据失败:', error);
    }
  }

  // 迁移商业信息
  private async migrateBusiness(): Promise<void> {
    const businessStoreData = localStorage.getItem('business-store');
    if (!businessStoreData) return;

    try {
      const parsed = JSON.parse(businessStoreData);
      const businessInfo: BusinessInfo = parsed.state?.businessInfo;

      if (businessInfo) {
        await dbManager.put('business', businessInfo);
        console.log('迁移了商业信息');
      }
    } catch (error) {
      console.error('迁移商业信息失败:', error);
    }
  }

  // 迁移用户数据
  private async migrateAuth(): Promise<void> {
    const authStoreData = localStorage.getItem('auth-store');
    if (!authStoreData) return;

    try {
      const parsed = JSON.parse(authStoreData);
      const user: User = parsed.state?.user;

      if (user) {
        await dbManager.put('users', user);
        console.log('迁移了用户数据');
      }
    } catch (error) {
      console.error('迁移用户数据失败:', error);
    }
  }

  // 迁移设置数据
  private async migrateSettings(): Promise<void> {
    const themeStoreData = localStorage.getItem('theme-store');
    if (themeStoreData) {
      try {
        const parsed = JSON.parse(themeStoreData);
        await dbManager.put('settings', {
          key: 'theme',
          value: parsed.state?.theme || 'system'
        });
        console.log('迁移了主题设置');
      } catch (error) {
        console.error('迁移主题设置失败:', error);
      }
    }
  }

  // 标记迁移完成
  private async markMigrationCompleted(): Promise<void> {
    await dbManager.put('settings', {
      key: 'migration_completed',
      value: true,
      timestamp: new Date().toISOString()
    });
  }

  // 清理 localStorage 数据（自动执行）
  async cleanupLocalStorage(): Promise<void> {
    try {
      // 自动清理所有相关的localStorage数据
      const keysToRemove = [
        'order-store',
        'product-store',
        'business-store',
        'auth-store',
        'theme-store',
        'app_users',
        'app_tokens'
      ];

      keysToRemove.forEach(key => {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key);
          console.log(`已清理 localStorage 键: ${key}`);
        }
      });

      console.log('localStorage 数据清理完成');
    } catch (error) {
      console.error('清理 localStorage 数据失败:', error);
    }
  }

  // 检查迁移状态
  async checkMigrationStatus(): Promise<boolean> {
    try {
      await dbManager.init();
      const migrationRecord = await dbManager.get('settings', 'migration_completed');
      return !!migrationRecord?.value;
    } catch (error) {
      console.error('检查迁移状态失败:', error);
      return false;
    }
  }
}

// 导出单例实例
export const dataMigration = DataMigrationManager.getInstance();
