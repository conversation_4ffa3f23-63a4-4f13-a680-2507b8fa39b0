'use client';

import { useEffect, useState } from 'react';
import { dataMigration } from '@/utils/dataMigration';
import { dataStore } from '@/utils/indexedDBAdapter';
import { useOrderStore } from '@/store/orderStore';
import { useProductStore } from '@/store/productStore';
import { useBusinessStore } from '@/store/businessStore';
import { useAuthStore } from '@/store/authStore';
import { useThemeStore } from '@/store/themeStore';

interface AppInitializerProps {
  children: React.ReactNode;
}

export function AppInitializer({ children }: AppInitializerProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { loadOrders } = useOrderStore();
  const { loadTemplates } = useProductStore();
  const { loadBusinessInfo } = useBusinessStore();
  const { checkAuth, user } = useAuthStore();
  const { initializeTheme, loadTheme } = useThemeStore();

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 1. 初始化 IndexedDB
      console.log('初始化 IndexedDB...');
      await dataStore.init();

      // 2. 检查是否需要数据迁移
      console.log('检查数据迁移...');
      const migrationCompleted = await dataMigration.checkMigrationStatus();

      if (!migrationCompleted) {
        console.log('开始数据迁移...');
        await dataMigration.migrate();

        // 询问是否清理 localStorage
        setTimeout(() => {
          dataMigration.cleanupLocalStorage().catch(console.error);
        }, 1000);
      }

      // 3. 加载数据到各个 store
      console.log('加载应用数据...');
      await Promise.all([
        loadOrders(),
        loadTemplates(),
        loadBusinessInfo()
      ]);

      setIsInitialized(true);
      console.log('应用初始化完成');

    } catch (error) {
      console.error('应用初始化失败:', error);
      setError(error instanceof Error ? error.message : '初始化失败');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            正在初始化应用...
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            请稍候，正在加载数据
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            初始化失败
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error}
          </p>
          <button
            onClick={initializeApp}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            应用未初始化
          </h2>
          <button
            onClick={initializeApp}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            初始化应用
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
