import { create } from 'zustand';
import { Order, OrderStore, ReceiptTemplate } from '@/types/order';
import { generateOrderId } from '@/utils/orderUtils';
import { dataStore } from '@/utils/indexedDBAdapter';
import { authService } from '@/services/authService';

// 默认模板
const defaultTemplates: ReceiptTemplate[] = [
  {
    id: 'modern',
    name: '现代风格',
    description: '简洁现代的设计风格，适合大多数业务场景',
    preview: '/templates/modern-preview.png',
    styles: {
      primaryColor: '#3B82F6',
      secondaryColor: '#F3F4F6',
      fontFamily: 'Inter, sans-serif',
      fontSize: '14px',
      layout: 'modern'
    }
  },
  {
    id: 'classic',
    name: '经典风格',
    description: '传统商务风格，正式专业',
    preview: '/templates/classic-preview.png',
    styles: {
      primaryColor: '#1F2937',
      secondaryColor: '#E5E7EB',
      fontFamily: 'Georgia, serif',
      fontSize: '14px',
      layout: 'classic'
    }
  },
  {
    id: 'minimal',
    name: '极简风格',
    description: '极简设计，突出重要信息',
    preview: '/templates/minimal-preview.png',
    styles: {
      primaryColor: '#000000',
      secondaryColor: '#FFFFFF',
      fontFamily: 'Helvetica, sans-serif',
      fontSize: '13px',
      layout: 'minimal'
    }
  }
];

export const useOrderStore = create<OrderStore>()((set, get) => ({
  currentOrder: null,
  orders: [],
  templates: defaultTemplates,
  selectedTemplate: defaultTemplates[0],
  isCloudSyncEnabled: false,
  isSyncing: false,
  lastSyncAt: null,
  syncError: null,

      setCurrentOrder: (order: Order) => {
        set({ currentOrder: order });
      },

      updateCurrentOrder: (updates: Partial<Order>) => {
        const { currentOrder } = get();
        if (currentOrder) {
          const updatedOrder = {
            ...currentOrder,
            ...updates,
            updatedAt: new Date().toISOString()
          };
          set({ currentOrder: updatedOrder });
        }
      },

      saveOrder: async (order: Order) => {
        const { orders, isCloudSyncEnabled } = get();
        const existingIndex = orders.findIndex(o => o.id === order.id);

        let finalOrder: Order;
        if (existingIndex >= 0) {
          // 更新现有订单
          finalOrder = {
            ...order,
            updatedAt: new Date().toISOString()
          };
          const updatedOrders = [...orders];
          updatedOrders[existingIndex] = finalOrder;
          set({ orders: updatedOrders });
        } else {
          // 添加新订单
          finalOrder = {
            ...order,
            id: order.id || generateOrderId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          set({ orders: [...orders, finalOrder] });
        }

        // 保存到 IndexedDB
        try {
          await dataStore.saveOrder(finalOrder);
        } catch (error) {
          console.error('Failed to save order to IndexedDB:', error);
        }

        // 如果启用了云端同步，自动同步到云端
        if (isCloudSyncEnabled) {
          get().syncToCloud().catch(console.error);
        }
      },

      deleteOrder: async (orderId: string) => {
        const { orders, isCloudSyncEnabled } = get();
        set({ orders: orders.filter(order => order.id !== orderId) });

        // 从 IndexedDB 删除
        try {
          await dataStore.deleteOrder(orderId);
        } catch (error) {
          console.error('Failed to delete order from IndexedDB:', error);
        }

        // 如果启用了云端同步，自动同步到云端
        if (isCloudSyncEnabled) {
          get().syncToCloud().catch(console.error);
        }
      },

      setSelectedTemplate: (template: ReceiptTemplate) => {
        set({ selectedTemplate: template });

        // 如果有当前订单，更新其模板
        const { currentOrder } = get();
        if (currentOrder) {
          get().updateCurrentOrder({ template });
        }
      },

      loadOrders: async () => {
        const { isCloudSyncEnabled } = get();

        // 首先从 IndexedDB 加载订单
        try {
          const orders = await dataStore.getAllOrders();
          set({ orders });
        } catch (error) {
          console.error('从 IndexedDB 加载订单失败:', error);
        }

        // 如果启用了云端同步，从云端加载订单
        if (isCloudSyncEnabled) {
          try {
            await get().syncFromCloud();
          } catch (error) {
            console.error('从云端加载订单失败:', error);
          }
        }
      },

      clearCurrentOrder: () => {
        set({ currentOrder: null });
      },

      // 云端同步功能
      syncToCloud: async () => {
        const { orders } = get();
        set({ isSyncing: true, syncError: null });

        try {
          await authService.syncOrdersToCloud(orders);
          set({
            lastSyncAt: new Date().toISOString(),
            isSyncing: false
          });
        } catch (error) {
          set({
            syncError: error instanceof Error ? error.message : '同步失败',
            isSyncing: false
          });
          throw error;
        }
      },

      syncFromCloud: async () => {
        set({ isSyncing: true, syncError: null });

        try {
          const cloudOrders = await authService.getOrdersFromCloud();
          set({
            orders: cloudOrders,
            lastSyncAt: new Date().toISOString(),
            isSyncing: false
          });
        } catch (error) {
          set({
            syncError: error instanceof Error ? error.message : '同步失败',
            isSyncing: false
          });
          throw error;
        }
      },

      enableCloudSync: () => {
        set({ isCloudSyncEnabled: true });
        // 启用后立即同步到云端
        get().syncToCloud().catch(console.error);
      },

      disableCloudSync: () => {
        set({
          isCloudSyncEnabled: false,
          lastSyncAt: null,
          syncError: null
        });
      },

      clearSyncError: () => {
        set({ syncError: null });
      }
    }));
