'use client';

import { useEffect, useState } from 'react';
import { useOrderStore } from '@/store/orderStore';
import { useAuthStore } from '@/store/authStore';
import { Cloud, CloudOff, RefreshCw, CheckCircle, AlertCircle, Wifi, WifiOff } from 'lucide-react';

export function SyncStatus() {
  const { 
    isCloudSyncEnabled, 
    isSyncing, 
    lastSyncAt, 
    syncError,
    syncToCloud,
    enableCloudSync,
    disableCloudSync,
    clearSyncError
  } = useOrderStore();
  
  const { isAuthenticated, user } = useAuthStore();
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleToggleSync = () => {
    if (isCloudSyncEnabled) {
      disableCloudSync();
    } else {
      enableCloudSync();
    }
  };

  const handleManualSync = () => {
    if (isAuthenticated && isOnline) {
      syncToCloud();
    }
  };

  const formatLastSync = (timestamp: string | null) => {
    if (!timestamp) return '从未同步';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`;
    return date.toLocaleDateString();
  };

  if (!isAuthenticated) {
    return (
      <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
        <CloudOff className="w-4 h-4" />
        <span className="text-sm">未登录</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-3">
      {/* 网络状态 */}
      <div className="flex items-center space-x-1">
        {isOnline ? (
          <Wifi className="w-4 h-4 text-green-500" />
        ) : (
          <WifiOff className="w-4 h-4 text-red-500" />
        )}
      </div>

      {/* 同步状态 */}
      <div className="flex items-center space-x-2">
        {isSyncing ? (
          <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
        ) : syncError ? (
          <AlertCircle className="w-4 h-4 text-red-500" />
        ) : isCloudSyncEnabled ? (
          <CheckCircle className="w-4 h-4 text-green-500" />
        ) : (
          <CloudOff className="w-4 h-4 text-gray-400" />
        )}

        <div className="text-sm">
          {isSyncing ? (
            <span className="text-blue-600 dark:text-blue-400">同步中...</span>
          ) : syncError ? (
            <span className="text-red-600 dark:text-red-400">同步失败</span>
          ) : isCloudSyncEnabled ? (
            <span className="text-green-600 dark:text-green-400">已启用云同步</span>
          ) : (
            <span className="text-gray-600 dark:text-gray-400">云同步已关闭</span>
          )}
        </div>
      </div>

      {/* 最后同步时间 */}
      {isCloudSyncEnabled && (
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {formatLastSync(lastSyncAt)}
        </div>
      )}

      {/* 控制按钮 */}
      <div className="flex items-center space-x-1">
        <button
          onClick={handleToggleSync}
          className={`p-1 rounded transition-colors ${
            isCloudSyncEnabled
              ? 'text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300'
              : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
          }`}
          title={isCloudSyncEnabled ? '关闭云同步' : '启用云同步'}
        >
          <Cloud className="w-4 h-4" />
        </button>

        {isCloudSyncEnabled && isOnline && (
          <button
            onClick={handleManualSync}
            disabled={isSyncing}
            className="p-1 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="手动同步"
          >
            <RefreshCw className={`w-4 h-4 ${isSyncing ? 'animate-spin' : ''}`} />
          </button>
        )}
      </div>

      {/* 错误提示 */}
      {syncError && (
        <div className="relative">
          <div className="absolute bottom-full right-0 mb-2 w-64 p-2 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-xs text-red-700 dark:text-red-300">
            {syncError}
            <button
              onClick={clearSyncError}
              className="ml-2 text-red-500 hover:text-red-700 dark:hover:text-red-400"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* 用户信息 */}
      {user && (
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {user.name}
        </div>
      )}
    </div>
  );
}
