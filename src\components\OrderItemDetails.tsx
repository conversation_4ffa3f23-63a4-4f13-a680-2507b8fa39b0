'use client';

import { OrderItem } from '@/types/order';
import { Package, Ruler, Calculator } from 'lucide-react';

interface OrderItemDetailsProps {
  item: OrderItem;
  showDetails?: boolean;
}

export function OrderItemDetails({ item, showDetails = false }: OrderItemDetailsProps) {
  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center">
          <Package className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <div>
            <h4 className="font-medium text-gray-900 dark:text-gray-100">{item.name}</h4>
            {item.category && (
              <span className="text-sm text-gray-500 dark:text-gray-400">{item.category}</span>
            )}
          </div>
        </div>
        <div className="text-right">
          <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            ¥{item.totalPrice.toFixed(2)}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {item.quantity} × ¥{item.unitPrice.toFixed(2)}
          </div>
        </div>
      </div>

      {item.description && (
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{item.description}</p>
      )}

      {showDetails && (
        <div className="space-y-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          {/* 规格信息 */}
          {item.dimensions && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              <div className="flex items-center mb-2">
                <Ruler className="w-4 h-4 text-gray-600 dark:text-gray-400 mr-2" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">商品规格</span>
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                {item.dimensions.length && (
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">长度:</span>
                    <span className="ml-1 text-gray-900 dark:text-gray-100">{item.dimensions.length}mm</span>
                  </div>
                )}
                {item.dimensions.width && (
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">宽度:</span>
                    <span className="ml-1 text-gray-900 dark:text-gray-100">{item.dimensions.width}mm</span>
                  </div>
                )}
                {item.dimensions.height && (
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">高度:</span>
                    <span className="ml-1 text-gray-900 dark:text-gray-100">{item.dimensions.height}mm</span>
                  </div>
                )}
                {item.dimensions.weight && (
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">重量:</span>
                    <span className="ml-1 text-gray-900 dark:text-gray-100">{item.dimensions.weight}kg</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 计价信息 */}
          {item.pricingMethod && (
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
              <div className="flex items-center mb-2">
                <Calculator className="w-4 h-4 text-blue-600 dark:text-blue-400 mr-2" />
                <span className="text-sm font-medium text-blue-700 dark:text-blue-300">计价信息</span>
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-blue-600 dark:text-blue-400">计价方式:</span>
                  <span className="ml-1 text-blue-900 dark:text-blue-100">
                    {item.pricingMethod === 'area' && '按面积'}
                    {item.pricingMethod === 'length' && '按长度'}
                    {item.pricingMethod === 'weight' && '按重量'}
                    {item.pricingMethod === 'unit' && '按件'}
                  </span>
                </div>
                <div>
                  <span className="text-blue-600 dark:text-blue-400">单位:</span>
                  <span className="ml-1 text-blue-900 dark:text-blue-100">{item.unit}</span>
                </div>
                {item.calculatedQuantity !== undefined && (
                  <div>
                    <span className="text-blue-600 dark:text-blue-400">计算数量:</span>
                    <span className="ml-1 text-blue-900 dark:text-blue-100">{item.calculatedQuantity.toFixed(2)}</span>
                  </div>
                )}
                {item.actualQuantity !== undefined && (
                  <div>
                    <span className="text-blue-600 dark:text-blue-400">实际数量:</span>
                    <span className="ml-1 text-blue-900 dark:text-blue-100">{item.actualQuantity.toFixed(2)}</span>
                  </div>
                )}
                {item.minQuantity && (
                  <div>
                    <span className="text-blue-600 dark:text-blue-400">最小计量:</span>
                    <span className="ml-1 text-blue-900 dark:text-blue-100">{item.minQuantity}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 模板信息 */}
          {item.templateId && (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              <span>模板: {item.templateName || item.templateId}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
