export interface CustomerInfo {
  name: string;
  phone: string;
  email?: string;
}

export interface AddressInfo {
  shippingAddress: string;
  billingAddress?: string;
}

export interface ProductDimensions {
  length?: number;
  width?: number;
  height?: number;
  weight?: number;
}

export interface OrderItem {
  id: string;
  name: string;
  category?: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  // 商品规格信息
  dimensions?: ProductDimensions;
  calculatedQuantity?: number;
  actualQuantity?: number;
  pricingMethod?: 'area' | 'length' | 'weight' | 'unit';
  unit?: string;
  minQuantity?: number;
  // 模板信息
  templateId?: string;
  templateName?: string;
}

export interface OrderInfo {
  id: string;
  orderNumber: string;
  date: string;
  items: OrderItem[];
  subtotal: number;
  tax?: number;
  discount?: number;
  total: number;
  notes?: string;
  paymentMethod?: string;
  paymentStatus?: 'pending' | 'paid' | 'cancelled';
}

export interface Order {
  id: string;
  customer: CustomerInfo;
  address: AddressInfo;
  orderInfo: OrderInfo;
  template: ReceiptTemplate;
  userId?: string; // 关联的用户ID，支持云端同步
  createdAt: string;
  updatedAt: string;
}

export interface ReceiptTemplate {
  id: string;
  name: string;
  description: string;
  preview: string;
  styles: {
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
    fontSize: string;
    layout: 'modern' | 'classic' | 'minimal';
  };
}

export interface ExportOptions {
  format: 'pdf' | 'png';
  quality?: number;
  size?: 'a4' | 'letter' | 'custom';
  orientation?: 'portrait' | 'landscape';
}

export interface OrderStore {
  currentOrder: Order | null;
  orders: Order[];
  templates: ReceiptTemplate[];
  selectedTemplate: ReceiptTemplate | null;
  isCloudSyncEnabled: boolean;
  isSyncing: boolean;
  lastSyncAt: string | null;
  syncError: string | null;

  // Actions
  setCurrentOrder: (order: Order) => void;
  updateCurrentOrder: (updates: Partial<Order>) => void;
  saveOrder: (order: Order) => Promise<void>;
  deleteOrder: (orderId: string) => Promise<void>;
  setSelectedTemplate: (template: ReceiptTemplate) => void;
  loadOrders: () => void;
  clearCurrentOrder: () => void;

  // Cloud sync actions
  syncToCloud: () => Promise<void>;
  syncFromCloud: () => Promise<void>;
  enableCloudSync: () => void;
  disableCloudSync: () => void;
  clearSyncError: () => void;
}
