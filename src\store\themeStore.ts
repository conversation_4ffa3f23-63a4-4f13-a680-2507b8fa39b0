import { create } from 'zustand';
import { dataStore } from '@/utils/indexedDBAdapter';

export type Theme = 'light' | 'dark' | 'system';

interface ThemeStore {
  theme: Theme;
  isDark: boolean;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  initializeTheme: () => void;
  loadTheme: () => Promise<void>;
}

export const useThemeStore = create<ThemeStore>()((set, get) => ({
  theme: 'system',
  isDark: false,

  setTheme: (theme: Theme) => {
    set({ theme });
    applyTheme(theme);
    // 保存到 IndexedDB
    dataStore.saveSetting('theme', theme).catch(console.error);
  },

  toggleTheme: () => {
    const { theme } = get();
    const newTheme = theme === 'light' ? 'dark' : 'light';
    get().setTheme(newTheme);
  },

  initializeTheme: () => {
    const { theme } = get();
    applyTheme(theme);
  },

  loadTheme: async () => {
    try {
      const savedTheme = await dataStore.getSetting('theme');
      if (savedTheme) {
        set({ theme: savedTheme });
        applyTheme(savedTheme);
      }
    } catch (error) {
      console.error('Failed to load theme from IndexedDB:', error);
    }
  }
}));

function applyTheme(theme: Theme) {
  const root = document.documentElement;

  if (theme === 'system') {
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    root.classList.toggle('dark', systemPrefersDark);
    useThemeStore.setState({ isDark: systemPrefersDark });
  } else {
    const isDark = theme === 'dark';
    root.classList.toggle('dark', isDark);
    useThemeStore.setState({ isDark });
  }
}

// 监听系统主题变化
if (typeof window !== 'undefined') {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  mediaQuery.addEventListener('change', (e) => {
    const { theme } = useThemeStore.getState();
    if (theme === 'system') {
      const root = document.documentElement;
      root.classList.toggle('dark', e.matches);
      useThemeStore.setState({ isDark: e.matches });
    }
  });
}
