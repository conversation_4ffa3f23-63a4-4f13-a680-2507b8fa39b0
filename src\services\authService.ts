import { LoginCredentials, RegisterCredentials, User } from '@/types/auth';
import { Order } from '@/types/order';
import { dataStore } from '@/utils/indexedDBAdapter';

// 模拟 API 延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟用户数据库 - 使用IndexedDB替代localStorage
const TOKENS_KEY = 'auth_token';

interface StoredUser extends User {
  password: string;
}

class AuthService {
  private async getUsers(): Promise<StoredUser[]> {
    try {
      await dataStore.init();
      return await dataStore.getAllUsers();
    } catch (error) {
      console.error('Failed to get users:', error);
      return [];
    }
  }

  private async saveUser(user: StoredUser): Promise<void> {
    try {
      await dataStore.init();
      await dataStore.saveUser(user);
    } catch (error) {
      console.error('Failed to save user:', error);
    }
  }

  private generateToken(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  private generateUserId(): string {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
  }

  private async setToken(token: string): Promise<void> {
    try {
      await dataStore.init();
      await dataStore.saveSetting(TOKENS_KEY, token);
    } catch (error) {
      console.error('Failed to save token:', error);
    }
  }

  private async getToken(): Promise<string | null> {
    try {
      await dataStore.init();
      return await dataStore.getSetting(TOKENS_KEY);
    } catch (error) {
      console.error('Failed to get token:', error);
      return null;
    }
  }

  private async removeToken(): Promise<void> {
    try {
      await dataStore.init();
      await dataStore.deleteSetting(TOKENS_KEY);
    } catch (error) {
      console.error('Failed to remove token:', error);
    }
  }

  async login(credentials: LoginCredentials): Promise<User> {
    await delay(1000); // 模拟网络延迟

    const users = await this.getUsers();
    const user = users.find(u => u.email === credentials.email);

    if (!user) {
      throw new Error('用户不存在');
    }

    if (user.password !== credentials.password) {
      throw new Error('密码错误');
    }

    const token = this.generateToken();
    await this.setToken(token);

    // 返回用户信息（不包含密码）
    const { password, ...userInfo } = user;
    return userInfo;
  }

  async register(credentials: RegisterCredentials): Promise<User> {
    await delay(1000); // 模拟网络延迟

    if (credentials.password !== credentials.confirmPassword) {
      throw new Error('两次输入的密码不一致');
    }

    if (credentials.password.length < 6) {
      throw new Error('密码长度至少为6位');
    }

    const users = await this.getUsers();

    // 检查邮箱是否已存在
    if (users.some(u => u.email === credentials.email)) {
      throw new Error('该邮箱已被注册');
    }

    const newUser: StoredUser = {
      id: this.generateUserId(),
      name: credentials.name,
      email: credentials.email,
      password: credentials.password,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await this.saveUser(newUser);

    const token = this.generateToken();
    await this.setToken(token);

    // 返回用户信息（不包含密码）
    const { password, ...userInfo } = newUser;
    return userInfo;
  }

  async getCurrentUser(): Promise<User | null> {
    await delay(500); // 模拟网络延迟

    const token = await this.getToken();
    if (!token) {
      return null;
    }

    // 在真实应用中，这里会验证 token 的有效性
    // 现在我们简单地检查是否有 token
    const users = await this.getUsers();
    if (users.length === 0) {
      return null;
    }

    // 模拟：返回最后登录的用户（在真实应用中应该通过 token 解析用户）
    const lastUser = users[users.length - 1];
    const { password, ...userInfo } = lastUser;
    return userInfo;
  }

  async updateProfile(updates: Partial<User>): Promise<User> {
    await delay(800); // 模拟网络延迟

    const token = await this.getToken();
    if (!token) {
      throw new Error('未登录');
    }

    const users = await this.getUsers();
    const currentUser = await this.getCurrentUser();

    if (!currentUser) {
      throw new Error('用户不存在');
    }

    const userIndex = users.findIndex(u => u.id === currentUser.id);
    if (userIndex === -1) {
      throw new Error('用户不存在');
    }

    // 更新用户信息
    const updatedUser = {
      ...users[userIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await this.saveUser(updatedUser);

    const { password, ...userInfo } = updatedUser;
    return userInfo;
  }

  logout(): void {
    this.removeToken();
  }

  // 同步订单到云端（模拟）
  async syncOrdersToCloud(orders: Order[]): Promise<void> {
    await delay(1500); // 模拟网络延迟

    const currentUser = await this.getCurrentUser();
    if (!currentUser) {
      throw new Error('未登录');
    }

    // 在真实应用中，这里会将订单数据发送到服务器
    // 现在我们将其存储在 IndexedDB 中，使用用户 ID 作为键
    try {
      await dataStore.init();
      const userOrdersKey = `cloud_orders_${currentUser.id}`;
      const existingOrdersData = await dataStore.getSetting(userOrdersKey);
      const existingOrders = existingOrdersData || [];

      // 合并订单（避免重复）
      const mergedOrders = [...existingOrders];

      orders.forEach(newOrder => {
        const existingIndex = mergedOrders.findIndex(order => order.id === newOrder.id);
        if (existingIndex >= 0) {
          // 更新现有订单
          mergedOrders[existingIndex] = newOrder;
        } else {
          // 添加新订单
          mergedOrders.push(newOrder);
        }
      });

      await dataStore.saveSetting(userOrdersKey, mergedOrders);

      console.log(`已同步 ${orders.length} 个订单到云端`);
    } catch (error) {
      console.error('同步订单到云端失败:', error);
      throw error;
    }
  }

  // 从云端获取订单（模拟）
  async getOrdersFromCloud(): Promise<Order[]> {
    await delay(1000); // 模拟网络延迟

    const currentUser = await this.getCurrentUser();
    if (!currentUser) {
      throw new Error('未登录');
    }

    try {
      await dataStore.init();
      const userOrdersKey = `cloud_orders_${currentUser.id}`;
      const orders = await dataStore.getSetting(userOrdersKey);

      return orders || [];
    } catch (error) {
      console.error('从云端获取订单失败:', error);
      return [];
    }
  }
}

export const authService = new AuthService();
