import { create } from 'zustand';
import { AuthStore, LoginCredentials, RegisterCredentials, User } from '@/types/auth';
import { authService } from '@/services/authService';
import { dataStore } from '@/utils/indexedDBAdapter';

export const useAuthStore = create<AuthStore>()((set, get) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,

  login: async (credentials: LoginCredentials) => {
    set({ isLoading: true, error: null });

    try {
      const user = await authService.login(credentials);

      // 设置当前用户ID到数据存储
      dataStore.setCurrentUserId(user.id);

      set({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });

      // 登录成功后同步本地数据到云端
      await get().syncLocalDataToCloud();
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : '登录失败',
        isLoading: false
      });
      throw error;
    }
  },

  register: async (credentials: RegisterCredentials) => {
    set({ isLoading: true, error: null });

    try {
      const user = await authService.register(credentials);

      // 设置当前用户ID到数据存储
      dataStore.setCurrentUserId(user.id);

      set({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });

      // 注册成功后同步本地数据到云端
      await get().syncLocalDataToCloud();
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : '注册失败',
        isLoading: false
      });
      throw error;
    }
  },

  logout: () => {
    authService.logout();
    // 清除当前用户ID
    dataStore.setCurrentUserId(null);
    set({
      user: null,
      isAuthenticated: false,
      error: null
    });
  },

      clearError: () => {
        set({ error: null });
      },

  checkAuth: async () => {
    set({ isLoading: true });

    try {
      const user = await authService.getCurrentUser();
      if (user) {
        // 设置当前用户ID到数据存储
        dataStore.setCurrentUserId(user.id);
        set({
          user,
          isAuthenticated: true,
          isLoading: false
        });
      } else {
        dataStore.setCurrentUserId(null);
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false
        });
      }
    } catch (error) {
      dataStore.setCurrentUserId(null);
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: error instanceof Error ? error.message : '认证检查失败'
      });
    }
  },

      updateProfile: async (updates: Partial<User>) => {
        const { user } = get();
        if (!user) return;

        set({ isLoading: true, error: null });

        try {
          const updatedUser = await authService.updateProfile(updates);
          set({
            user: updatedUser,
            isLoading: false
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : '更新失败',
            isLoading: false
          });
          throw error;
        }
      },

  // 同步本地数据到云端
  syncLocalDataToCloud: async () => {
    const { user } = get();
    if (!user) return;

    try {
      // 获取IndexedDB中的订单数据
      const orders = await dataStore.getAllOrders();

      if (orders.length > 0) {
        // 同步到云端
        await authService.syncOrdersToCloud(orders);
        console.log(`已同步 ${orders.length} 个订单到云端`);
      }
    } catch (error) {
      console.error('同步数据到云端失败:', error);
    }
  }
}));
