import { StateStorage } from 'zustand/middleware';
import { dbManager } from './indexedDB';

// IndexedDB 存储适配器
export class IndexedDBAdapter implements StateStorage {
  private storeName: string;

  constructor(storeName: string) {
    this.storeName = storeName;
  }

  async getItem(name: string): Promise<string | null> {
    try {
      await dbManager.init();
      const data = await dbManager.get('settings', `${this.storeName}_${name}`);
      return data ? JSON.stringify(data.value) : null;
    } catch (error) {
      console.error(`Failed to get item ${name}:`, error);
      return null;
    }
  }

  async setItem(name: string, value: string): Promise<void> {
    try {
      await dbManager.init();
      await dbManager.put('settings', {
        key: `${this.storeName}_${name}`,
        value: JSON.parse(value),
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error(`Failed to set item ${name}:`, error);
    }
  }

  async removeItem(name: string): Promise<void> {
    try {
      await dbManager.init();
      await dbManager.delete('settings', `${this.storeName}_${name}`);
    } catch (error) {
      console.error(`Failed to remove item ${name}:`, error);
    }
  }
}

// 创建专用的数据存储类
export class OrderDataStore {
  private currentUserId: string | null = null;

  async init(): Promise<void> {
    await dbManager.init();
  }

  // 设置当前用户ID
  setCurrentUserId(userId: string | null): void {
    this.currentUserId = userId;
  }

  // 获取当前用户ID
  getCurrentUserId(): string | null {
    return this.currentUserId;
  }

  // 订单相关方法
  async saveOrder(order: any): Promise<void> {
    const orderWithUser = {
      ...order,
      userId: this.currentUserId || 'anonymous'
    };
    await dbManager.put('orders', orderWithUser);
  }

  async getOrder(id: string): Promise<any> {
    return await dbManager.get('orders', id);
  }

  async getAllOrders(): Promise<any[]> {
    if (this.currentUserId) {
      return await dbManager.getByIndex('orders', 'by_user', this.currentUserId);
    }
    return await dbManager.getByIndex('orders', 'by_user', 'anonymous');
  }

  async deleteOrder(id: string): Promise<void> {
    await dbManager.delete('orders', id);
  }

  async getOrdersByCustomer(customerName: string): Promise<any[]> {
    const allOrders = await this.getAllOrders();
    return allOrders.filter(order => order.customer?.name === customerName);
  }

  // 商品相关方法
  async saveProduct(product: any): Promise<void> {
    const productWithUser = {
      ...product,
      userId: this.currentUserId || 'anonymous'
    };
    await dbManager.put('products', productWithUser);
  }

  async getProduct(id: string): Promise<any> {
    return await dbManager.get('products', id);
  }

  async getAllProducts(): Promise<any[]> {
    if (this.currentUserId) {
      return await dbManager.getByIndex('products', 'by_user', this.currentUserId);
    }
    return await dbManager.getByIndex('products', 'by_user', 'anonymous');
  }

  async deleteProduct(id: string): Promise<void> {
    await dbManager.delete('products', id);
  }

  async getProductsByCategory(category: string): Promise<any[]> {
    const allProducts = await this.getAllProducts();
    return allProducts.filter(product => product.category === category);
  }

  // 商业信息相关方法
  async saveBusiness(business: any): Promise<void> {
    const businessWithUser = {
      ...business,
      userId: this.currentUserId || 'anonymous'
    };
    await dbManager.put('business', businessWithUser);
  }

  async getBusiness(id: string): Promise<any> {
    return await dbManager.get('business', id);
  }

  async getAllBusiness(): Promise<any[]> {
    if (this.currentUserId) {
      return await dbManager.getByIndex('business', 'by_user', this.currentUserId);
    }
    return await dbManager.getByIndex('business', 'by_user', 'anonymous');
  }

  // 用户相关方法
  async saveUser(user: any): Promise<void> {
    await dbManager.put('users', user);
  }

  async getUser(id: string): Promise<any> {
    return await dbManager.get('users', id);
  }

  async getUserByEmail(email: string): Promise<any> {
    const users = await dbManager.getByIndex('users', 'by_email', email);
    return users[0] || null;
  }

  async getAllUsers(): Promise<any[]> {
    return await dbManager.getAll('users');
  }

  async deleteUser(id: string): Promise<void> {
    await dbManager.delete('users', id);
  }

  // 设置相关方法
  async saveSetting(key: string, value: any): Promise<void> {
    const userKey = this.currentUserId ? `${this.currentUserId}_${key}` : `anonymous_${key}`;
    await dbManager.put('settings', {
      key: userKey,
      value,
      userId: this.currentUserId || 'anonymous',
      timestamp: new Date().toISOString()
    });
  }

  async getSetting(key: string): Promise<any> {
    const userKey = this.currentUserId ? `${this.currentUserId}_${key}` : `anonymous_${key}`;
    const setting = await dbManager.get('settings', userKey);
    return setting?.value || null;
  }

  async deleteSetting(key: string): Promise<void> {
    const userKey = this.currentUserId ? `${this.currentUserId}_${key}` : `anonymous_${key}`;
    await dbManager.delete('settings', userKey);
  }

  // 统计方法
  async getOrderCount(): Promise<number> {
    return await dbManager.count('orders');
  }

  async getProductCount(): Promise<number> {
    return await dbManager.count('products');
  }

  async getUserCount(): Promise<number> {
    return await dbManager.count('users');
  }

  // 清理方法
  async clearAllOrders(): Promise<void> {
    await dbManager.clear('orders');
  }

  async clearAllProducts(): Promise<void> {
    await dbManager.clear('products');
  }

  async clearAllUsers(): Promise<void> {
    await dbManager.clear('users');
  }

  async clearAllSettings(): Promise<void> {
    await dbManager.clear('settings');
  }
}

// 创建全局数据存储实例
export const dataStore = new OrderDataStore();
