import { create } from 'zustand';
import { BusinessInfo, BusinessStore, DEFAULT_BUSINESS_INFO } from '@/types/business';
import { dataStore } from '@/utils/indexedDBAdapter';

function generateBusinessId(): string {
  return `business_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
}

// 文件转 base64
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
}

export const useBusinessStore = create<BusinessStore>()((set, get) => ({
  businessInfo: null,

      updateBusinessInfo: async (updates) => {
        const { businessInfo } = get();

        let finalInfo: BusinessInfo;
        if (businessInfo) {
          finalInfo = {
            ...businessInfo,
            ...updates,
            updatedAt: new Date().toISOString()
          };
        } else {
          // 如果没有现有信息，创建新的
          finalInfo = {
            ...DEFAULT_BUSINESS_INFO,
            ...updates,
            id: generateBusinessId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
        }

        // 保存到 IndexedDB
        try {
          await dataStore.saveBusiness(finalInfo);
        } catch (error) {
          console.error('Failed to save business info to IndexedDB:', error);
        }

        set({ businessInfo: finalInfo });
      },

      setBusinessInfo: async (info) => {
        // 保存到 IndexedDB
        try {
          await dataStore.saveBusiness(info);
        } catch (error) {
          console.error('Failed to save business info to IndexedDB:', error);
        }

        set({ businessInfo: info });
      },

      clearBusinessInfo: () => {
        set({ businessInfo: null });
      },

      loadBusinessInfo: async () => {
        try {
          const businessList = await dataStore.getAllBusiness();
          const businessInfo = businessList.length > 0 ? businessList[0] : null;
          set({ businessInfo });
        } catch (error) {
          console.error('Failed to load business info from IndexedDB:', error);
        }
      },

      uploadQRCode: async (file) => {
        try {
          // 验证文件类型
          if (!file.type.startsWith('image/')) {
            throw new Error('请选择图片文件');
          }

          // 验证文件大小 (最大 2MB)
          if (file.size > 2 * 1024 * 1024) {
            throw new Error('图片大小不能超过 2MB');
          }

          const base64 = await fileToBase64(file);

          // 更新商业信息中的二维码
          get().updateBusinessInfo({ qrCode: base64 });

          return base64;
        } catch (error) {
          throw error;
        }
      },

      uploadLogo: async (file) => {
        try {
          // 验证文件类型
          if (!file.type.startsWith('image/')) {
            throw new Error('请选择图片文件');
          }

          // 验证文件大小 (最大 2MB)
          if (file.size > 2 * 1024 * 1024) {
            throw new Error('图片大小不能超过 2MB');
          }

          const base64 = await fileToBase64(file);

          // 更新商业信息中的 logo
          get().updateBusinessInfo({ logo: base64 });

          return base64;
        } catch (error) {
          throw error;
        }
      }
    }));
