export type PricingMethod = 'unit' | 'area' | 'length' | 'weight';

export interface ProductTemplate {
  id: string;
  name: string;
  category: string;
  unitPrice: number;
  pricingMethod: PricingMethod;
  unit: string; // 单位：个、平方米、米、公斤等
  minQuantity?: number; // 最小计量，如不足1平方米按1平方米计算
  description?: string;
  userId?: string; // 关联的用户ID，支持云端同步
  createdAt: string;
  updatedAt: string;
}

export interface ProductDimensions {
  length?: number;
  width?: number;
  height?: number;
  weight?: number;
}

export interface CalculatedProduct {
  template: ProductTemplate;
  dimensions?: ProductDimensions;
  calculatedQuantity: number;
  actualQuantity: number; // 实际计量（考虑最小计量）
  unitPrice: number;
  totalPrice: number;
}

export interface ProductStore {
  templates: ProductTemplate[];
  selectedTemplate: ProductTemplate | null;

  // Actions
  addTemplate: (template: Omit<ProductTemplate, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateTemplate: (id: string, updates: Partial<ProductTemplate>) => Promise<void>;
  deleteTemplate: (id: string) => Promise<void>;
  loadTemplates: () => Promise<void>;
  setSelectedTemplate: (template: ProductTemplate | null) => void;
  getTemplatesByCategory: (category: string) => ProductTemplate[];
  calculateProduct: (template: ProductTemplate, dimensions?: ProductDimensions) => CalculatedProduct;
}

// 预设商品类别
export const PRODUCT_CATEGORIES = [
  '纱窗',
  '门窗',
  '五金配件',
  '玻璃',
  '密封条',
  '其他'
] as const;

// 计价方式选项
export const PRICING_METHODS = [
  { value: 'unit', label: '按件计价', unit: '件' },
  { value: 'area', label: '按面积计价', unit: '平方米' },
  { value: 'length', label: '按长度计价', unit: '米' },
  { value: 'weight', label: '按重量计价', unit: '公斤' }
] as const;
